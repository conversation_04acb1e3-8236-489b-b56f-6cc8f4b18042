﻿using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Integration;

public class TradingServiceIntegrationTests
{
    private readonly Mock<ISignalGenerator> _mockSignalGenerator;
    private readonly Mock<IRiskManager> _mockRiskManager;
    private readonly Mock<IPortfolioGate> _mockPortfolioGate;
    private readonly Mock<ITradeExecutor> _mockTradeExecutor;
    private readonly Mock<IStopManager> _mockStopManager;
    private readonly Mock<ITradingSafetyGuard> _mockSafetyGuard;
    private readonly Mock<IMarketRegimeService> _mockRegimeService;
    private readonly SimpleTradingService _tradingService;

    public TradingServiceIntegrationTests()
    {
        _mockSignalGenerator = new Mock<ISignalGenerator>();
        _mockRiskManager = new Mock<IRiskManager>();
        _mockPortfolioGate = new Mock<IPortfolioGate>();
        _mockTradeExecutor = new Mock<ITradeExecutor>();
        _mockStopManager = new Mock<IStopManager>();
        _mockSafetyGuard = new Mock<ITradingSafetyGuard>();
        _mockRegimeService = new Mock<IMarketRegimeService>();

        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));

        _tradingService = new SimpleTradingService(
            _mockSignalGenerator.Object,
            _mockRiskManager.Object,
            _mockPortfolioGate.Object,
            _mockTradeExecutor.Object,
            _mockStopManager.Object,
            _mockSafetyGuard.Object,
            _mockRegimeService.Object,
            Mock.Of<ILogger<SimpleTradingService>>());
    }

    [TestTimeout(TestTimeouts.Integration)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateBlocks_DoesNotExecuteTrades()
    {
        // Arrange
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(false);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSignalGenerator.Verify(x => x.RunAsync(It.IsAny<int>()), Times.Never);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateAllows_ExecutesFullCycle()
    {
        // Arrange
        var signals = new[]
        {
            new TradingSignal("AAPL", 150m, 3m, 0.15m),
            new TradingSignal("MSFT", 300m, 5m, 0.20m)
        };

        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(10m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Exactly(2));
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), 10m), Times.Exactly(2));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenZeroQuantity_SkipsTrade()
    {
        // Arrange
        var signals = new[]
        {
            new TradingSignal("AAPL", 150m, 3m, 0.15m)
        };

        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(0m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WithMixedQuantities_ExecutesOnlyValidTrades()
    {
        // Arrange
        var signals = new[]
        {
            new TradingSignal("AAPL", 150m, 3m, 0.15m),
            new TradingSignal("MSFT", 300m, 5m, 0.20m),
            new TradingSignal("GOOGL", 2500m, 50m, 0.10m)
        };

        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);

        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(signals[0])).ReturnsAsync(5m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(signals[1])).ReturnsAsync(0m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(signals[2])).ReturnsAsync(2m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(signals[0], 5m), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(signals[1], It.IsAny<decimal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(signals[2], 2m), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenRegimeBlocksTrading_DoesNotExecuteTrades()
    {
        // Arrange
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(false);
        _mockRegimeService.Setup(x => x.GetCachedRegimeAsync(It.IsAny<CancellationToken>())).ReturnsAsync(MarketRegime.Volatile);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Never);
        _mockSignalGenerator.Verify(x => x.RunAsync(It.IsAny<int>()), Times.Never);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenRegimeAllowsTrading_ProceedsToPortfolioGate()
    {
        // Arrange
        var signals = new[]
        {
            new TradingSignal("AAPL", 150m, 3m, 0.15m)
        };

        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(10m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), 10m), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Integration)]
    [Theory]
    [InlineData(MarketRegime.TrendingDown, false)]
    [InlineData(MarketRegime.Volatile, true)] // High risk tolerance allows volatile trading
    [InlineData(MarketRegime.TrendingUp, true)]
    [InlineData(MarketRegime.Sideways, true)]
    public async Task ExecuteCycleAsync_WithDifferentRegimes_BehavesCorrectly(MarketRegime regime, bool shouldTrade)
    {
        // Arrange
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(shouldTrade);
        _mockRegimeService.Setup(x => x.GetCachedRegimeAsync(It.IsAny<CancellationToken>())).ReturnsAsync(regime);

        if (shouldTrade)
        {
            _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
            _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(new List<TradingSignal>());
        }

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>()), Times.Once);

        if (shouldTrade)
        {
            _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Once);
            _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        }
        else
        {
            _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Never);
            _mockSignalGenerator.Verify(x => x.RunAsync(It.IsAny<int>()), Times.Never);
        }
    }
}
