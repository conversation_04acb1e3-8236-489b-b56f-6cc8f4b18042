using System;
using Xunit;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Attribute to specify test timeout in milliseconds
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class TestTimeoutAttribute : Attribute
{
    public int TimeoutMs { get; }

    public TestTimeoutAttribute(int timeoutMs)
    {
        TimeoutMs = timeoutMs;
    }
}

/// <summary>
/// Predefined timeout constants for different test categories
/// </summary>
public static class TestTimeouts
{
    /// <summary>
    /// Fast unit tests - 2 seconds (optimized for performance)
    /// </summary>
    public const int Unit = 2_000;

    /// <summary>
    /// Integration tests - 15 seconds (reduced from 30s)
    /// </summary>
    public const int Integration = 15_000;

    /// <summary>
    /// Performance tests - 30 seconds (reduced from 60s)
    /// </summary>
    public const int Performance = 30_000;

    /// <summary>
    /// Database tests - 5 seconds (optimized for in-memory databases)
    /// </summary>
    public const int Database = 5_000;

    /// <summary>
    /// Network/API tests - 10 seconds (optimized for mocked services)
    /// </summary>
    public const int Network = 10_000;

    /// <summary>
    /// Streaming tests - 15 seconds (optimized for mocked connections)
    /// </summary>
    public const int Streaming = 15_000;
}
