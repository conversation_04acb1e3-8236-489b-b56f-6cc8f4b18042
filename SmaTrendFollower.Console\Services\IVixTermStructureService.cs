using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for VIX term structure analysis service
/// Provides comprehensive volatility surface and term structure analysis
/// </summary>
public interface IVixTermStructureService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when VIX term structure changes significantly
    /// </summary>
    event EventHandler<VixTermStructureChangeEventArgs>? TermStructureChanged;
    
    /// <summary>
    /// Fired when contango/backwardation regime changes
    /// </summary>
    event EventHandler<VolatilityRegimeChangeEventArgs>? VolatilityRegimeChanged;
    
    /// <summary>
    /// Fired when VIX spike is detected
    /// </summary>
    event EventHandler<VixSpikeEventArgs>? VixSpikeDetected;
    
    // === Properties ===
    
    /// <summary>
    /// Current VIX term structure regime
    /// </summary>
    VixTermStructureRegime CurrentRegime { get; }
    
    /// <summary>
    /// Current VIX level
    /// </summary>
    decimal CurrentVix { get; }
    
    /// <summary>
    /// Current term structure slope
    /// </summary>
    decimal TermStructureSlope { get; }
    
    // === Analysis Methods ===
    
    /// <summary>
    /// Gets current VIX term structure analysis
    /// </summary>
    Task<VixTermStructureAnalysis> GetTermStructureAnalysisAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Analyzes VIX futures curve
    /// </summary>
    Task<VixFuturesCurveAnalysis> AnalyzeFuturesCurveAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Detects VIX spikes and regime changes
    /// </summary>
    Task<VixSpikeAnalysis> DetectVixSpikesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Calculates volatility surface metrics
    /// </summary>
    Task<VolatilitySurfaceAnalysis> AnalyzeVolatilitySurfaceAsync(string symbol, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets VIX-based position sizing recommendations
    /// </summary>
    Task<VixPositionSizingRecommendation> GetPositionSizingRecommendationAsync(decimal basePositionSize, CancellationToken cancellationToken = default);
    
    // === Historical Analysis ===
    
    /// <summary>
    /// Gets historical VIX term structure data
    /// </summary>
    Task<IEnumerable<VixTermStructureSnapshot>> GetHistoricalTermStructureAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Analyzes VIX mean reversion patterns
    /// </summary>
    Task<VixMeanReversionAnalysis> AnalyzeMeanReversionAsync(TimeSpan lookbackPeriod, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets VIX percentile ranking
    /// </summary>
    Task<VixPercentileAnalysis> GetVixPercentileAsync(TimeSpan lookbackPeriod, CancellationToken cancellationToken = default);
    
    // === Monitoring ===
    
    /// <summary>
    /// Starts real-time VIX monitoring
    /// </summary>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops VIX monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// VIX term structure regime types
/// </summary>
public enum VixTermStructureRegime
{
    SteepContango,      // M1 << M2 << M3 (normal, low volatility)
    ModerateContango,   // M1 < M2 < M3 (normal)
    FlatContango,       // M1 ≈ M2 ≈ M3 (transitional)
    Backwardation,      // M1 > M2 > M3 (stressed, high volatility)
    SteepBackwardation  // M1 >> M2 >> M3 (crisis, extreme volatility)
}

/// <summary>
/// VIX term structure analysis result
/// </summary>
public readonly record struct VixTermStructureAnalysis(
    DateTime AnalysisTime,
    VixTermStructureRegime Regime,
    decimal CurrentVix,
    decimal Vix9Day,
    decimal Vix30Day,
    decimal Vix90Day,
    decimal TermStructureSlope,
    decimal ContangoBackwardationRatio,
    string RegimeDescription,
    List<VixTermPoint> TermStructure,
    VixImplications TradingImplications
);

/// <summary>
/// VIX term structure point
/// </summary>
public readonly record struct VixTermPoint(
    int DaysToExpiration,
    decimal ImpliedVolatility,
    decimal Price,
    string ContractSymbol
);

/// <summary>
/// VIX futures curve analysis
/// </summary>
public readonly record struct VixFuturesCurveAnalysis(
    DateTime AnalysisTime,
    List<VixFuturesContract> Contracts,
    decimal CurveSlope,
    decimal CurveCurvature,
    bool IsInContango,
    decimal ContangoStrength,
    string CurveShape,
    List<string> TradingSignals
);

/// <summary>
/// VIX futures contract data
/// </summary>
public readonly record struct VixFuturesContract(
    string Symbol,
    DateTime ExpirationDate,
    int DaysToExpiration,
    decimal Price,
    decimal Volume,
    decimal OpenInterest,
    decimal PremiumToSpot
);

/// <summary>
/// VIX spike analysis
/// </summary>
public readonly record struct VixSpikeAnalysis(
    DateTime AnalysisTime,
    bool IsSpikeDetected,
    decimal CurrentVix,
    decimal BaselineVix,
    decimal SpikeIntensity,
    VixSpikeType SpikeType,
    TimeSpan SpikeDuration,
    decimal MeanReversionTarget,
    List<string> SpikeCharacteristics
);

/// <summary>
/// VIX spike types
/// </summary>
public enum VixSpikeType
{
    None,
    Minor,      // 1-2 standard deviations
    Moderate,   // 2-3 standard deviations
    Major,      // 3-4 standard deviations
    Extreme     // >4 standard deviations
}

/// <summary>
/// Volatility surface analysis
/// </summary>
public readonly record struct VolatilitySurfaceAnalysis(
    string Symbol,
    DateTime AnalysisTime,
    decimal AtmImpliedVolatility,
    decimal VolatilitySkew,
    decimal VolatilitySmile,
    decimal TermStructureSlope,
    Dictionary<int, Dictionary<decimal, decimal>> VolatilitySurface, // DTE -> Strike -> IV
    List<string> SurfaceCharacteristics
);

/// <summary>
/// VIX-based position sizing recommendation
/// </summary>
public readonly record struct VixPositionSizingRecommendation(
    decimal RecommendedSize,
    decimal VixAdjustmentFactor,
    decimal RiskReductionFactor,
    VixRegimeRisk RegimeRisk,
    string Reasoning,
    List<string> RiskWarnings
);

/// <summary>
/// VIX regime risk levels
/// </summary>
public enum VixRegimeRisk
{
    VeryLow,    // VIX < 12
    Low,        // VIX 12-16
    Normal,     // VIX 16-20
    Elevated,   // VIX 20-30
    High,       // VIX 30-40
    Extreme     // VIX > 40
}

/// <summary>
/// VIX term structure snapshot
/// </summary>
public readonly record struct VixTermStructureSnapshot(
    DateTime Date,
    decimal Vix,
    decimal Vix9Day,
    decimal Vix30Day,
    decimal Vix90Day,
    VixTermStructureRegime Regime,
    decimal TermStructureSlope
);

/// <summary>
/// VIX mean reversion analysis
/// </summary>
public readonly record struct VixMeanReversionAnalysis(
    decimal CurrentVix,
    decimal LongTermMean,
    decimal MeanReversionSpeed,
    decimal ExpectedReversionTime,
    decimal ReversionTarget,
    decimal ReversionProbability,
    string Analysis
);

/// <summary>
/// VIX percentile analysis
/// </summary>
public readonly record struct VixPercentileAnalysis(
    decimal CurrentVix,
    decimal Percentile,
    decimal PercentileRank,
    bool IsExtreme,
    string PercentileDescription,
    List<VixPercentileLevel> HistoricalLevels
);

/// <summary>
/// VIX percentile level
/// </summary>
public readonly record struct VixPercentileLevel(
    decimal Percentile,
    decimal VixLevel,
    string Description
);

/// <summary>
/// VIX trading implications
/// </summary>
public readonly record struct VixImplications(
    string MarketSentiment,
    string VolatilityExpectation,
    string PositionSizingGuidance,
    string HedgingRecommendation,
    List<string> TradingOpportunities,
    List<string> RiskWarnings
);

/// <summary>
/// Event arguments for VIX term structure changes
/// </summary>
public class VixTermStructureChangeEventArgs : EventArgs
{
    public required VixTermStructureRegime PreviousRegime { get; init; }
    public required VixTermStructureRegime CurrentRegime { get; init; }
    public required decimal TermStructureChange { get; init; }
    public required DateTime ChangeTime { get; init; }
    public required string Description { get; init; }
}

/// <summary>
/// Event arguments for volatility regime changes
/// </summary>
public class VolatilityRegimeChangeEventArgs : EventArgs
{
    public required VixRegimeRisk PreviousRisk { get; init; }
    public required VixRegimeRisk CurrentRisk { get; init; }
    public required decimal VixChange { get; init; }
    public required DateTime ChangeTime { get; init; }
    public required string Trigger { get; init; }
}

/// <summary>
/// Event arguments for VIX spikes
/// </summary>
public class VixSpikeEventArgs : EventArgs
{
    public required VixSpikeAnalysis SpikeAnalysis { get; init; }
    public required DateTime DetectedAt { get; init; }
    public required string AlertMessage { get; init; }
}
