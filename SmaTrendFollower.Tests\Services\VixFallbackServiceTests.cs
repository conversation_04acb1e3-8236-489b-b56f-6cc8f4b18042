using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using Xunit;
using Moq.Protected;
using System.Net;

namespace SmaTrendFollower.Tests.Services;

public class VixFallbackServiceTests : IDisposable
{
    private readonly Mock<ILogger<VixFallbackService>> _mockLogger;
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private readonly HttpClient _httpClient;
    private readonly VixFallbackService _service;

    public VixFallbackServiceTests()
    {
        _mockLogger = new Mock<ILogger<VixFallbackService>>();
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

        // Create HttpClient with mocked message handler
        _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

        _mockHttpClientFactory.Setup(x => x.CreateClient("vix-fallback"))
            .Returns(_httpClient);

        _service = new VixFallbackService(
            _mockLogger.Object,
            _mockAlpacaFactory.Object,
            _mockHttpClientFactory.Object);
    }

    [Fact]
    public async Task GetVixFromWebAsync_ShouldReturnNull_WhenNoSourcesAvailable()
    {
        // Arrange
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _service.GetVixFromWebAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldReturnNull_WhenNoEtfDataAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<LatestMarketDataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((IBar?)null);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldCalculateFromVxx_WhenVxxDataAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(25.0m); // VXX price
        mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddMinutes(-5)); // Fresh data (5 minutes old)

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<LatestMarketDataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBar.Object);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeLessOrEqualTo(80); // Sanity check for VIX range

        // VXX * 1.45 + 8.2 = 25 * 1.45 + 8.2 = 44.45
        result.Should().BeApproximately(44.45m, 2.0m);
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldCalculateFromUvxy_WhenOnlyUvxyAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(10.0m); // UVXY price
        mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddMinutes(-3)); // Fresh data (3 minutes old)

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<LatestMarketDataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBar.Object);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeLessOrEqualTo(80); // Sanity check for VIX range

        // UVXY * 0.85 + 12.5 = 10 * 0.85 + 12.5 = 21.0
        result.Should().BeApproximately(21.0m, 2.0m);
    }

    [Fact]
    public async Task GetVixFromBraveSearchAsync_ShouldReturnNull_WhenHttpRequestsFail()
    {
        // Arrange - Mock HTTP requests to fail for all financial sites
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _service.GetVixFromBraveSearchAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldClampValues_WhenCalculationOutOfRange()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(200.0m); // Extremely high VXX price
        mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddMinutes(-1)); // Fresh data (1 minute old)

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<LatestMarketDataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBar.Object);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeLessOrEqualTo(80m); // Should be clamped to max
        result.Should().BeGreaterOrEqualTo(10m); // Should be clamped to min
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldReturnNull_WhenAllEtfDataIsStale()
    {
        // Arrange - Test the 18-minute staleness rule that's critical for production
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(25.0m); // Valid VXX price
        mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddMinutes(-20)); // STALE data (20 minutes old)

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<LatestMarketDataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBar.Object);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert - Should return null because all ETF data is stale (>18 minutes)
        result.Should().BeNull("because ETF data older than 18 minutes should be rejected to prevent stale trading decisions");
    }

    public void Dispose()
    {
        _service?.Dispose();
        _httpClient?.Dispose();
    }
}
